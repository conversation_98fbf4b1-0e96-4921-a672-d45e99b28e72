"""API router."""
from fastapi import APIRouter

from app.api.v1.endpoints import stories, interactions, health, debug, cache, themes, theme_mapping, mcp, simple_theme_api

# Create API router with /api/v1 prefix
api_router = APIRouter(prefix="/api/v1")

# Include all endpoint routers
api_router.include_router(stories.router, prefix="/stories", tags=["stories"])
api_router.include_router(interactions.router, prefix="/interactions", tags=["interactions"])
api_router.include_router(health.router, tags=["health"])
api_router.include_router(debug.router, tags=["debug"])
api_router.include_router(cache.router, prefix="/cache", tags=["cache"])
api_router.include_router(themes.router, prefix="/themes", tags=["themes"])
api_router.include_router(theme_mapping.router, prefix="/theme-mapping", tags=["theme-mapping"])
api_router.include_router(mcp.router, prefix="/mcp", tags=["mcp"])
api_router.include_router(simple_theme_api.router, prefix="/simple-themes", tags=["simple-themes"])