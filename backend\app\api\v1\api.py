"""API router."""
from fastapi import APIRouter

from app.api.v1.endpoints import stories, interactions, health, debug, cache, themes, contextual_theme_api, admin_theme_relations

# Create API router with /api/v1 prefix
api_router = APIRouter(prefix="/api/v1")

# Include all endpoint routers
api_router.include_router(stories.router, prefix="/stories", tags=["stories"])
api_router.include_router(interactions.router, prefix="/interactions", tags=["interactions"])
api_router.include_router(health.router, tags=["health"])
api_router.include_router(debug.router, tags=["debug"])
api_router.include_router(cache.router, prefix="/cache", tags=["cache"])
api_router.include_router(themes.router, prefix="/themes", tags=["themes"])
api_router.include_router(contextual_theme_api.router, prefix="/contextual-themes", tags=["contextual-themes"])
api_router.include_router(admin_theme_relations.router, prefix="/admin/theme-relations", tags=["admin", "theme-relations"])
api_router.include_router(contextual_theme_api.router, prefix="/contextual-themes", tags=["contextual-themes"])
api_router.include_router(admin_theme_relations.router, prefix="/admin/theme-relations", tags=["admin", "theme-relations"])